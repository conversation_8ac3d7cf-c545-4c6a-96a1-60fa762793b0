#!/usr/bin/env tsx

/**
 * Script para testar manualmente o acesso de administradores
 * 
 * Este script verifica se os administradores têm acesso a todos os cursos
 * e recursos do sistema, conforme as correções implementadas.
 */

import { db } from '@repo/database';

async function testAdminAccess() {
  console.log('🔍 Testando acesso de administradores...\n');

  try {
    // 1. Verificar se existem usuários admin
    const adminUsers = await db.user.findMany({
      where: { role: 'admin' },
      select: { id: true, name: true, email: true, role: true }
    });

    console.log('👑 Usuários Admin encontrados:', adminUsers.length);
    adminUsers.forEach(admin => {
      console.log(`  - ${admin.name} (${admin.email})`);
    });

    if (adminUsers.length === 0) {
      console.log('⚠️  Nenhum usuário admin encontrado. Criando um para teste...');
      
      const testAdmin = await db.user.create({
        data: {
          name: 'Test Admin',
          email: '<EMAIL>',
          emailVerified: true,
          role: 'admin',
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      });
      
      console.log(`✅ Admin de teste criado: ${testAdmin.name} (${testAdmin.email})`);
      adminUsers.push(testAdmin);
    }

    // 2. Verificar cursos disponíveis
    const courses = await db.courses.findMany({
      include: {
        organization: { select: { name: true, slug: true } },
        creator: { select: { name: true, email: true } }
      }
    });

    console.log(`\n📚 Cursos disponíveis: ${courses.length}`);
    courses.forEach(course => {
      console.log(`  - ${course.name} (Org: ${course.organization.name})`);
    });

    // 3. Verificar UserCourses para admins
    const adminUser = adminUsers[0];
    const adminCourseAccess = await db.userCourses.findMany({
      where: { userId: adminUser.id },
      include: { course: { select: { name: true } } }
    });

    console.log(`\n🎓 Cursos com acesso direto para ${adminUser.name}: ${adminCourseAccess.length}`);
    adminCourseAccess.forEach(access => {
      console.log(`  - ${access.course.name}`);
    });

    // 4. Simular verificação de acesso (lógica dos endpoints corrigidos)
    console.log('\n🔐 Simulando verificação de acesso para admin:');
    
    for (const course of courses.slice(0, 3)) { // Testar apenas os primeiros 3 cursos
      const hasDirectAccess = adminCourseAccess.some(access => access.courseId === course.id);
      const isCreator = course.createdBy === adminUser.id;
      const isAdmin = adminUser.role === 'admin';
      
      console.log(`\n  📖 Curso: ${course.name}`);
      console.log(`    - Acesso direto (UserCourses): ${hasDirectAccess ? '✅' : '❌'}`);
      console.log(`    - É criador do curso: ${isCreator ? '✅' : '❌'}`);
      console.log(`    - É admin (bypass): ${isAdmin ? '✅' : '❌'}`);
      
      // Lógica corrigida: admin sempre tem acesso
      const shouldHaveAccess = isAdmin || hasDirectAccess || isCreator;
      console.log(`    - 🎯 Resultado: ${shouldHaveAccess ? '✅ ACESSO PERMITIDO' : '❌ ACESSO NEGADO'}`);
    }

    // 5. Verificar organizações
    const organizations = await db.organization.findMany({
      select: { id: true, name: true, slug: true }
    });

    console.log(`\n🏢 Organizações disponíveis: ${organizations.length}`);
    organizations.forEach(org => {
      console.log(`  - ${org.name} (${org.slug})`);
    });

    // 6. Verificar memberships do admin
    const adminMemberships = await db.member.findMany({
      where: { userId: adminUser.id },
      include: { organization: { select: { name: true, slug: true } } }
    });

    console.log(`\n👥 Memberships do admin: ${adminMemberships.length}`);
    adminMemberships.forEach(membership => {
      console.log(`  - ${membership.organization.name} (${membership.role})`);
    });

    // 7. Simular verificação de acesso a organizações
    console.log('\n🏢 Simulando verificação de acesso a organizações para admin:');
    
    for (const org of organizations.slice(0, 3)) {
      const hasMembership = adminMemberships.some(m => m.organizationId === org.id);
      const isAdmin = adminUser.role === 'admin';
      
      console.log(`\n  🏢 Organização: ${org.name}`);
      console.log(`    - Tem membership: ${hasMembership ? '✅' : '❌'}`);
      console.log(`    - É admin (bypass): ${isAdmin ? '✅' : '❌'}`);
      
      // Lógica corrigida: admin sempre tem acesso
      const shouldHaveAccess = isAdmin || hasMembership;
      console.log(`    - 🎯 Resultado: ${shouldHaveAccess ? '✅ ACESSO PERMITIDO' : '❌ ACESSO NEGADO'}`);
    }

    console.log('\n✅ Teste de acesso de administradores concluído!');
    console.log('\n📋 Resumo das correções implementadas:');
    console.log('  ✅ get-course-preview.ts - Admin bypass adicionado');
    console.log('  ✅ get-module-lessons.ts - Verificação de acesso ao curso adicionada');
    console.log('  ✅ get-course-lessons.ts - Verificação de acesso ao curso adicionada');
    console.log('  ✅ get-vitrines.ts - Admin bypass adicionado');
    console.log('  ✅ get-organization-courses.ts - Admin bypass adicionado');
    console.log('  ✅ get-organization-vitrines.ts - Admin bypass adicionado');
    console.log('  ✅ get-vitrine.ts - Admin bypass adicionado');
    console.log('  ✅ lesson-files.ts - Admin bypass adicionado (2 endpoints)');
    console.log('  ✅ member-area-settings.ts - Admin bypass adicionado (3 endpoints)');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o teste
testAdminAccess().catch(console.error);
